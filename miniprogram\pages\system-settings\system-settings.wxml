<!--system-settings.wxml-->
<!-- High-end Redesign -->
<view class="container">
  <!-- 预约取消时间设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="time" size="24" />
      <text class="card-title">预约取消时间设置</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">当前设置</view>
        <view class="time-setting-value">
          <text class="time-value">{{formattedTime}}</text>
          <text class="time-unit">{{timeUnit}}</text>
        </view>
      </view>
      <view class="input-row">
        <text class="input-label">活动开始前</text>
        <t-input
          type="number"
          placeholder="分钟"
          value="{{cancelTimeLimit}}"
          bind:change="onCancelTimeChange"
          bind:blur="onCancelTimeBlur"
          class="time-input"
        />
        <text class="input-suffix">分钟内不可取消</text>
      </view>
      <view class="setting-description">
        设置学员可在活动开始前多少分钟取消预约。例如，输入180，则表示活动开始前3小时内无法取消。
      </view>
    </view>
  </view>

  <!-- 联系信息设置卡片 -->
  <view class="setting-card">
    <view class="card-header">
      <t-icon name="call" size="24" />
      <text class="card-title">联系信息设置</text>
    </view>
    <view class="card-body">
      <view class="setting-item">
        <view class="setting-label">联系电话</view>
        <view class="setting-input">
          <t-input
            placeholder="请输入联系电话"
            value="{{contactPhone}}"
            bind:change="onContactPhoneChange"
            class="contact-input"
            align="right"
          />
        </view>
      </view>
      <view class="setting-item">
        <view class="setting-label">门店地址</view>
        <view class="setting-input">
          <t-input
            placeholder="请输入门店地址"
            value="{{contactAddress}}"
            bind:change="onContactAddressChange"
            class="contact-input"
            align="right"
          />
        </view>
      </view>
      <view class="setting-item">
        <view class="setting-label">门店公告</view>
      </view>
      <t-textarea
        placeholder="请输入门店公告，将显示在首页"
        value="{{contactAnnouncement}}"
        bind:change="onContactAnnouncementChange"
        class="contact-textarea"
        maxlength="500"
        autosize
      />
      <view class="setting-description">
        联系信息将显示在首页，供用户查看。
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-section">
    <t-button
      theme="primary"
      size="large"
      block
      bind:tap="saveSettings"
      loading="{{isSaving}}"
      class="save-btn"
    >
      保存设置
    </t-button>
  </view>

  <!-- 提示组件区域 -->
  <t-toast id="t-toast" />
  <t-message id="t-message" />
  <t-dialog id="t-dialog" />
</view>
