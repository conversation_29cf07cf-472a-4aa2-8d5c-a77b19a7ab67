// index.js
// 微信小程序首页逻辑文件
// 本文件相当于 Web 项目的 JS 脚本文件（如 main.js），用于处理页面数据、事件和与后端的交互
// 小程序采用 Page() 方法注册页面，类似于 Vue 的 export default 或 React 的函数组件

/**
 * 模块导入说明
 *
 * ES6模块导入语法：import { 函数名 } from '路径'
 * 类似于其他语言的导入机制：
 * - C#: using System; 或 using static Math;
 * - Java: import java.util.List; 或 import static Math.*;
 * - Python: from module import function
 *
 * 解构导入：{ showToast, showLoading, hideToast }
 * 从模块中只导入需要的函数，而不是整个模块
 * 这样可以减少内存占用，提升性能
 */

// 导入Toast工具函数：用于显示消息提示
// 路径解析：../../ 表示向上两级目录，然后进入utils文件夹
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

// 导入系统设置工具函数：用于加载联系信息
import { loadContactInfo } from '../../utils/systemSettings.js';

/**
 * Page() 函数是小程序的核心函数之一，用于注册一个页面
 * 接收一个对象作为参数，这个对象包含页面的数据、生命周期函数和事件处理函数等
 * 类似于 Vue 中的 new Vue() 或 React 中的 React.Component 类
 */
Page({
  /**
   * data 对象用于存储页面的响应式数据
   * 类似于 Vue 的 data 选项或 React 的 state
   * 页面中可以通过 this.data 访问这些数据
   * 修改数据需要使用 this.setData() 方法，而不是直接赋值
   */
  data: {
    loading: true,
    

    /**
     * 轮播图相关数据
     *
     * 设计说明：
     * 首页轮播图数据可以是静态的（直接在WXML中定义）
     * 也可以是动态的（从数据库或相册管理中获取）
     * 当前实现采用混合模式：静态图片+动态相册图片
     */

    // 静态图片数据已直接在WXML中使用，不再需要在data中定义
    // 如果需要完全动态的轮播图数据，可以在这里定义：
    // swiperList: [
    //   {image: '图片路径1', title: '标题1', link: '跳转链接1'},
    //   {image: '图片路径2', title: '标题2', link: '跳转链接2'}
    // ]

    /**
     * 视差滚动效果相关数据
     *
     * 视差滚动：背景图片滚动速度与内容滚动速度不同，产生层次感
     * 实现原理：监听页面滚动，动态调整背景图片的transform属性
     */

    // 背景图样式：存储动态计算的CSS样式字符串
    // 数据类型：string
    // 格式：'transform: translateY(Npx);'
    // 用途：通过style属性应用到背景元素上，实现视差效果
    parallaxStyle: '',

    // 滚动距离：记录页面当前的滚动位置
    // 数据类型：number
    // 单位：像素(px)
    // 用途：计算视差效果的偏移量，触发相关动画
    scrollTop: 0,

    // 是否正在刷新：控制下拉刷新的状态
    // 数据类型：boolean
    // true：正在执行刷新操作，显示刷新动画
    // false：未在刷新状态，允许用户触发新的刷新
    isRefreshing: false,





    /**
     * 联系信息数据
     *
     * 数据来源：从云数据库的系统设置表中获取
     * 设计理念：不使用硬编码，支持管理员动态修改
     * 更新机制：页面加载时从数据库获取最新信息
     */

    // 联系电话：健身房的客服电话
    // 数据类型：string
    // 格式：'138-0000-0000' 或 '************'
    // 用途：用户点击电话按钮时拨打此号码
    // 默认值：空字符串，加载完成后更新为实际电话
    contactPhone: '',

    // 联系地址：健身房的详细地址
    // 数据类型：string
    // 格式：'省市区街道门牌号'
    // 用途：显示在地址弹窗中，用户可复制或导航
    // 默认值：空字符串，加载完成后更新为实际地址
    contactAddress: '',

    // 联系公告：健身房的重要通知或公告
    // 数据类型：string
    // 内容：营业时间、优惠活动、重要通知等
    // 用途：显示在公告弹窗中，向用户传达重要信息
    // 默认值：空字符串，加载完成后更新为实际公告
    contactAnnouncement: '',

    /**
     * 弹窗控制状态
     *
     * 用途：控制各种信息弹窗的显示和隐藏
     * 设计模式：每个弹窗都有独立的控制变量
     */

    // 门店公告弹窗显示状态：控制公告弹窗的显示
    // 数据类型：boolean
    // true：显示公告弹窗，用户可查看详细公告内容
    // false：隐藏公告弹窗
    // 触发：用户点击公告按钮时设为true
    showAnnouncement: false,

    // 门店地址弹窗显示状态：控制地址弹窗的显示
    // 数据类型：boolean
    // true：显示地址弹窗，用户可查看详细地址并复制
    // false：隐藏地址弹窗
    // 触发：用户点击地址按钮时设为true
    showAddress: false,

    /**
     * 相册管理相关数据
     *
     * 功能说明：
     * 首页可以展示从相册管理中选中的图片
     * 管理员在后台相册管理中标记图片为"首页展示"
     * 这些图片会在首页的轮播图或图片展示区域显示
     */

    // 首页展示图片：存储首页轮播图的图片数据
    // 数据类型：Array<Object>
    // 数据结构：[{url: '临时访问地址', originalUrl: '原始地址', title: '标题'}]
    // 数据来源：从相册管理表中查询标记为"首页展示"的图片
    // 临时地址：云存储的临时访问地址，有时效性
    bannerImages: [],

    // 相册所有图片：存储相册管理中的所有图片
    // 数据类型：Array<Object>
    // 数据结构：[{url: '临时访问地址', originalUrl: '原始地址', description: '描述'}]
    // 用途：用户点击查看更多图片时显示
    // 更新时机：页面加载时从album_images表获取
    albumImages: [],

    // 相册弹窗显示状态：控制相册图片弹窗的显示
    // 数据类型：boolean
    // true：显示相册弹窗，用户可浏览所有相册图片
    // false：隐藏相册弹窗
    // 触发：用户点击"查看更多"或图片时设为true
    showAlbumPopup: false,

    // 相册弹窗当前索引：控制弹窗中显示的图片索引
    // 数据类型：number
    // 范围：0 到 albumImages.length - 1
    // 用途：在弹窗的轮播图中定位当前显示的图片
    // 更新：用户滑动轮播图时更新此值
    albumPopupIndex: 0,


  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 生命周期对比：
   * - 小程序：onLoad → onShow → onReady
   * - Vue：created → mounted
   * - React：constructor → componentDidMount
   * - C#：Page_Load事件
   * - Java：@PostConstruct注解的方法
   *
   * @param {Object} options - 页面参数对象
   *   包含页面跳转时传递的参数，类似于Web的URL查询参数
   *   例如：/pages/index/index?id=123，则options = {id: "123"}
   */
  onLoad(options) {
    /**
     * 页面初始化流程
     *
     * 执行顺序说明：
     * 这些初始化操作按照依赖关系和重要性排序
     * 1. 资源预加载（提升用户体验）
     * 2. 基础信息加载（核心功能数据）
     * 3. 展示内容加载（页面内容数据）
     */

    /**
     * 第一步：预加载背景图片资源
     *
     * 目的：提前加载图片资源，避免用户看到图片加载过程
     * 技术原理：使用wx.getImageInfo()预加载图片到缓存
     * 用户体验：页面打开时背景图片立即显示，无加载延迟
     * 类比技术：类似于Web的Image预加载或React的图片懒加载
     */
    this.preloadBackgroundImage();

    /**
     * 第二步：加载联系信息
     *
     * 数据来源：云数据库的系统设置表
     * 加载内容：门店电话、地址、公告等联系信息
     * 异步处理：不阻塞页面渲染，数据加载完成后自动更新UI
     * 错误处理：加载失败时使用默认值，不影响页面正常显示
     * 类比技术：类似于Web项目中调用API获取系统配置信息
     */
    this.loadContactInfo();

    /**
     * 第三步：获取首页展示图片
     *
     * 数据来源：云数据库的相册管理表
     * 筛选条件：标记为"首页展示"的图片
     * 处理逻辑：获取云存储图片的临时访问链接
     * 显示位置：首页轮播图或图片展示区域
     * 类比技术：类似于Web项目中获取banner图片列表的API调用
     */
    this.getBannerImages();

    // Hide loading animation after a delay
    setTimeout(() => {
      // 第一步：触发隐藏动画
      // 将动画状态设置为 'hiding'，这会给 loading-container 添加一个 'hiding' 类
      // CSS中我们定义了 .loading-container.hiding { opacity: 0; }
      // 并且 .loading-container 有一个 transition: opacity 0.5s;
      // 所以这个操作会触发一个持续0.5秒的淡出动画
      this.setData({ loadingAnimationState: 'hiding' });

      // 第二步：在动画结束后真正移除DOM
      // 我们设置一个500毫秒（0.5秒）的延迟，等待淡出动画执行完毕
      // 然后再设置 loading: false，此时 wx:if="{{loading}}" 条件为假，整个加载容器将从页面结构中移除
      // 这样做可以确保用户能看到完整的淡出效果，而不是元素突然消失
      setTimeout(() => {
        this.setData({ loading: false });
      }, 500); // 这个时间必须等于或大于CSS中淡出动画的持续时间
    }, 2000); // 2-second delay for demonstration



    /**
     * 保留代码说明
     *
     * 以下被注释的代码是为了未来扩展而保留：
     * this.getImageTempUrls(); - 轮播图临时链接获取功能
     *
     * 注释原因：
     * 1. 当前页面使用静态图片，不需要动态轮播图
     * 2. 保留代码便于未来添加轮播图功能
     * 3. 避免不必要的网络请求，提升页面加载速度
     *
     * 开发实践：
     * 在实际项目中，注释代码通常用于：
     * - 暂时不用但可能后续需要的功能
     * - 调试时临时禁用的代码
     * - 有问题需要修复但不影响主流程的代码
     */
    // this.getImageTempUrls();

    
  },

  // 获取云存储图片的临时访问链接
  // 此函数当前未被使用，因为页面使用的是静态图片而非轮播图
  // 保留此函数是为了未来可能添加轮播图功能时使用
  async getImageTempUrls() {
    try {
      // 检查swiperList是否存在
      if (!this.data.swiperList || !Array.isArray(this.data.swiperList)) {
        return;
      }
      
      showLoading(this, '加载中...');
      
      // 获取所有图片的fileID
      const fileList = this.data.swiperList.map(item => item.image);
      
      // 调用云函数获取临时访问链接
      const result = await wx.cloud.getTempFileURL({
        fileList: fileList
      });
      
      // 更新轮播图数据，使用临时访问链接
      const newSwiperList = this.data.swiperList.map((item, index) => {
        return {
          ...item,
          image: result.fileList[index].tempFileURL
        };
      });
      
      this.setData({
        swiperList: newSwiperList
      });
      
      hideToast(this);
    } catch (error) {
      hideToast(this);
      // 移除错误提示，因为即使临时链接获取失败，图片可能已经通过原始路径加载成功
      // showToast(this, { message: '加载图片失败', theme: 'error' });
    }
  },

  /**
   * onShow: 页面生命周期函数 - 页面显示时调用
   *
   * 触发时机：
   * 1. 页面首次加载显示时（在onLoad之后）
   * 2. 从其他页面返回到当前页面时
   * 3. 从后台切换到前台时
   *
   * 生命周期对比：
   * - 小程序：每次页面显示都会调用
   * - Vue：activated（keep-alive组件）
   * - React：componentDidUpdate（某些情况下）
   * - Android：onResume()
   * - iOS：viewWillAppear()
   */
  onShow() {
    // 设置自定义tabBar高亮当前页面
    // typeof检查类型，确保getTabBar方法存在且为函数类型
    // &&逻辑与运算符，只有前面条件为true时才执行后面的代码
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      // getTabBar()获取自定义tabBar组件实例
      // setData()更新tabBar组件的数据，类似于Vue的this.$set()
      this.getTabBar().setData({
        selected: 0    // 设置选中的tab索引，0表示第一个tab（首页）
      });
    }

    // 每次页面显示时刷新联系信息
    // 这样可以确保信息是最新的，特别是从设置页面返回时
    this.loadContactInfo();

    // 检查并更新Badge状态
    this.updateTabBarBadges();
  },

  /**
   * loadContactInfo: 加载联系信息的异步方法
   *
   * async/await语法说明：
   * - async：声明这是一个异步函数，返回Promise对象
   * - await：等待异步操作完成，暂停函数执行直到Promise resolve
   *
   * 错误处理模式：
   * 使用try-catch捕获异常，确保程序不会因为网络错误而崩溃
   * 这是现代JavaScript开发的最佳实践
   */
  async loadContactInfo() {
    /**
     * 异步数据加载的标准流程
     *
     * try-catch模式：现代JavaScript异步编程的最佳实践
     * 确保网络错误、数据库错误等不会导致程序崩溃
     */
    try {
      /**
       * 调用工具函数获取联系信息
       *
       * await关键字：暂停函数执行，等待Promise完成
       * 执行流程：
       * 1. 调用loadContactInfo()工具函数
       * 2. 工具函数查询云数据库的系统设置表
       * 3. 返回包含联系信息的对象
       * 4. 将结果赋值给contactInfo变量
       *
       * 工具函数位置：utils/systemSettings.js
       * 返回格式：{phone: string, address: string, announcement: string}
       */
      const contactInfo = await loadContactInfo();

      /**
       * 数据有效性检查
       *
       * 检查原因：
       * 1. 数据库可能为空（首次使用系统）
       * 2. 网络请求可能返回null
       * 3. 数据格式可能不正确
       *
       * 防御性编程：确保后续代码不会因为数据问题而出错
       */
      if (contactInfo) {
        /**
         * 更新页面数据
         *
         * setData方法：小程序的数据更新机制
         * 特点：
         * 1. 响应式更新：数据变化自动触发页面重新渲染
         * 2. 批量更新：一次setData可以更新多个字段
         * 3. 异步执行：不阻塞主线程，保证页面流畅
         *
         * 类比技术：
         * - Vue: this.$set() 或 响应式数据赋值
         * - React: this.setState() 或 useState的setter
         * - Angular: 双向数据绑定自动更新
         */
        this.setData({
          /**
           * 数据字段映射和默认值处理
           *
           * ||运算符（逻辑或）：提供默认值的常用模式
           * 工作原理：
           * 1. 如果contactInfo.phone有值，使用该值
           * 2. 如果contactInfo.phone为null/undefined/空字符串，使用''
           *
           * 好处：
           * 1. 避免页面显示undefined或null
           * 2. 确保数据类型一致（都是字符串）
           * 3. 提供良好的用户体验
           */

          // 联系电话：门店客服电话，用户可点击拨打
          contactPhone: contactInfo.phone || '',

          // 门店地址：详细地址信息，用户可查看和复制
          contactAddress: contactInfo.address || '',

          // 门店公告：重要通知，如营业时间、活动信息等
          contactAnnouncement: contactInfo.announcement || ''
        });
      } else {
        /**
         * 数据为空的处理
         *
         * 场景：
         * 1. 数据库中没有配置联系信息
         * 2. 查询结果为空
         * 3. 数据格式不正确
         *
         * 处理策略：
         * 设置为空字符串而不是保持undefined
         * 这样页面上不会显示异常的undefined文字
         */
        this.setData({
          contactPhone: '',      // 空电话：不显示电话按钮或显示默认提示
          contactAddress: '',    // 空地址：不显示地址按钮或显示默认提示
          contactAnnouncement: '' // 空公告：不显示公告按钮或显示默认提示
        });
      }
    } catch (error) {
      /**
       * 错误处理
       *
       * 可能的错误类型：
       * 1. 网络错误：网络连接失败、超时等
       * 2. 数据库错误：查询失败、权限不足等
       * 3. 数据格式错误：返回数据格式不符合预期
       * 4. 系统错误：云函数执行失败等
       *
       * 处理策略：
       * 1. 记录错误日志（便于调试）
       * 2. 设置默认值（保证页面正常显示）
       * 3. 不显示错误信息给用户（避免技术细节暴露）
       *
       * 用户体验考虑：
       * 联系信息加载失败不应该影响用户使用其他功能
       * 页面应该优雅降级，显示默认状态
       */

      // 记录错误日志：便于开发者调试和问题排查
      console.error('加载联系信息失败:', error);

      // 设置默认空值：确保页面正常显示，不显示错误信息
      this.setData({
        contactPhone: '',      // 默认空电话
        contactAddress: '',    // 默认空地址
        contactAnnouncement: '' // 默认空公告
      });

      // 在实际项目中，这里通常还会记录错误日志
      // console.error('加载联系信息失败:', error);
    }
  },

  // 轮播图切换事件
  onSwiperChange(e) {
    const { current, source } = e.detail;
    this.setData({
      current
    });
  },
  
  // 轮播图点击事件
  onSwiperClick(e) {
    const { index } = e.detail;
    // 这里可以添加点击轮播图后的跳转逻辑
  },

  /**
   * goToSchedule: 跳转到课程表页面的事件处理方法
   *
   * 事件绑定说明：
   * - 在WXML中通过 bind:tap="goToSchedule" 绑定点击事件
   * - 类似于HTML的 onclick="goToSchedule()"
   * - 类似于Vue的 @click="goToSchedule"
   * - 类似于React的 onClick={goToSchedule}
   *
   * 页面跳转类型对比：
   * - wx.switchTab: 跳转到tabBar页面（底部导航页面）
   * - wx.navigateTo: 跳转到普通页面（可返回）
   * - wx.redirectTo: 重定向到页面（不可返回）
   * - wx.reLaunch: 重启应用到指定页面
   */
  goToSchedule() {
    // wx.switchTab(): 切换到tabBar页面的专用方法
    // 只能用于跳转在app.json中配置的tabBar页面
    // 类似于Web单页应用的路由切换，但会清空页面栈中的非tabBar页面
    wx.switchTab({
      url: '/pages/schedule/schedule'  // 目标页面的绝对路径
    });
  },

  /**
   * goToMembershipCard: 跳转到考勤卡页面的事件处理方法
   *
   * 功能开发状态处理：
   * 在实际项目中，经常会有功能还在开发中的情况
   * 这时通常显示"开发中"提示，而不是跳转到空白页面
   */
  goToMembershipCard() {
    // showToast(): 显示消息提示的工具函数（从utils/toast.js导入）
    // 参数说明：
    // - this: 当前页面实例，用于绑定toast组件
    // - message: 提示文字内容
    // - theme: 提示主题样式（success成功、error错误、warning警告）
    // - duration: 显示时长（毫秒），2000表示2秒后自动消失
    showToast(this, {
      message: '功能开发中......',
      theme: 'warning',
      duration: 2000
    });
  },

  /**
   * goToCourseDetail: 跳转到课程详情页面的事件处理方法
   *
   * 事件对象和数据传递：
   * @param {Object} e - 事件对象，包含事件相关信息
   *   - e.currentTarget: 触发事件的元素（绑定事件的元素）
   *   - e.target: 实际被点击的元素（可能是子元素）
   *   - e.currentTarget.dataset: 元素上的data-*属性集合
   *
   * 数据传递方式对比：
   * - 小程序：通过data-*属性传递，在JS中通过e.currentTarget.dataset获取
   * - HTML：通过data-*属性，在JS中通过element.dataset获取
   * - Vue：通过@click="method(param)"直接传参
   * - React：通过onClick={() => method(param)}传参
   */
  goToCourseDetail(e) {
    // 从事件对象中获取课程数据
    // e.currentTarget.dataset.course 对应WXML中的 data-course="{{course}}"
    const course = e.currentTarget.dataset.course;

    // 页面跳转并传递参数
    // URL参数传递类似于Web的查询字符串：?id=123&name=abc
    // 模板字符串语法：`字符串${变量}`，ES6语法
    wx.navigateTo({
      url: `/pages/course-detail/course-detail?id=${course.id}`
    });

    // 目标页面可以在onLoad(options)中接收参数：
    // onLoad(options) {
    //   const courseId = options.id;
    // }
  },

  /**
   * onPageScroll: 页面滚动事件处理方法（带节流优化）
   *
   * 性能优化 - 节流(Throttle)：
   * 节流是一种性能优化技术，限制函数执行频率
   * 类似于游戏中的技能冷却时间，在指定时间内只能执行一次
   *
   * 节流 vs 防抖对比：
   * - 节流(Throttle)：固定时间间隔执行，如每16ms执行一次
   * - 防抖(Debounce)：延迟执行，如停止操作后500ms才执行
   *
   * 滚动事件特点：
   * - 触发频率极高，每秒可能触发几十次甚至上百次
   * - 不优化会导致页面卡顿、耗电量增加
   * - 需要节流来保证流畅性
   *
   * @param {Object} e - 滚动事件对象
   *   - e.scrollTop: 页面垂直滚动距离（像素）
   */
  onPageScroll: function(e) {
    // 节流处理：清除之前的定时器，避免频繁更新
    // 如果用户快速滚动，只有最后一次滚动会被处理
    if (this.scrollTimer) {
      // clearTimeout(): 清除定时器，类似于C#的Timer.Stop()
      clearTimeout(this.scrollTimer);
    }

    // setTimeout(): 设置延迟执行，类似于C#的Task.Delay()
    // 16ms约等于60fps的刷新率（1000ms/60≈16.67ms）
    // 这样可以保证动画流畅，同时不会过度消耗性能
    this.scrollTimer = setTimeout(() => {
      // 获取页面滚动距离
      const scrollTop = e.scrollTop;

      // 更新页面数据中的滚动距离
      // 这个数据可能被其他地方使用，比如显示"回到顶部"按钮
      this.setData({
        scrollTop: scrollTop
      });

      // 计算视差滚动效果的缩放比例
      // 视差滚动：背景图片随滚动产生缩放效果，增强视觉体验
      // 数学公式：scale = 1 + scrollTop * 系数
      // - 系数0.0005控制缩放速度，值越大缩放越快
      // - Math.min()限制最大缩放比例，避免图片过度放大
      const scale = Math.min(1 + scrollTop * 0.0005, 1.15);

      // 更新背景图片的CSS样式
      // transform: scale()用于缩放元素
      // translateZ(0)启用硬件加速，提升动画性能
      // 模板字符串语法：`字符串${变量}`
      this.setData({
        parallaxStyle: `transform: scale(${scale}) translateZ(0);`
      });
    }, 16); // 16毫秒延迟，约60fps
  },

  /**
   * onPullDownRefresh: 下拉刷新事件处理方法
   *
   * 下拉刷新机制：
   * 1. 用户在页面顶部下拉
   * 2. 触发onPullDownRefresh事件
   * 3. 执行数据刷新逻辑
   * 4. 调用wx.stopPullDownRefresh()结束刷新状态
   *
   * 需要在页面配置文件(index.json)中启用：
   * {
   *   "enablePullDownRefresh": true,
   *   "backgroundTextStyle": "dark"
   * }
   *
   * 类似功能对比：
   * - Web：手动实现下拉刷新或使用第三方库
   * - iOS：UIRefreshControl
   * - Android：SwipeRefreshLayout
   */
  onPullDownRefresh() {
    // 防重复刷新：检查是否正在刷新中
    // 这是一种防御性编程，避免用户多次触发刷新导致重复请求
    if (this.data.isRefreshing) {
      // wx.stopPullDownRefresh(): 停止下拉刷新的loading状态
      wx.stopPullDownRefresh();
      return; // 提前返回，不执行后续代码
    }

    // 设置刷新状态为true，防止重复刷新
    // 同时显示骨架屏，提升用户体验
    this.setData({
      isRefreshing: true
    });



    // 执行实际的数据刷新操作
    // 这里刷新首页展示图片，实际项目中可能还会刷新其他数据
    this.getBannerImages();

    // 重新加载联系信息
    this.loadContactInfo();

    // 模拟数据刷新过程
    // 实际项目中这里通常是异步的网络请求
    // setTimeout模拟网络请求的延迟时间
    setTimeout(() => {
      // 停止下拉刷新的loading动画
      wx.stopPullDownRefresh();

      // 重置刷新状态，
      this.setData({
        isRefreshing: false
      });

      // 显示刷新成功提示
      // 给用户反馈，提升用户体验
      showToast(this, {
        message: '刷新成功',
        theme: 'success',
        duration: 1500  // 1.5秒后自动消失
      });
    }, 1200); // 1.2秒延迟，模拟网络请求时间
  },

  // 预加载背景图片资源
  preloadBackgroundImage() {
    const bgImageUrl = 'cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/head.jpg';
    
    // 使用微信小程序的图片预加载API
    wx.getImageInfo({
      src: bgImageUrl,
      success: (res) => {
        // 预加载成功
      },
      fail: (err) => {
        // 预加载失败，但不影响页面正常显示
      }
    });
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }
  },

  // 显示门店公告弹出层
  showAnnouncementPopup() {
    this.setData({
      showAnnouncement: true
    });
  },

  // 关闭门店公告弹出层
  closeAnnouncementPopup() {
    this.setData({
      showAnnouncement: false
    });
  },

  showAddressPopup() {
    this.setData({
      showAddress: true
    });
  },
  closeAddressPopup() {
    this.setData({
      showAddress: false
    });
  },

  // 获取首页展示图片
  // 从云数据库获取管理员选择的图片，并获取临时访问链接
  // 如果加载失败，则不显示任何图片
  getBannerImages: function() {
    const db = wx.cloud.database();
    db.collection('album_images')
      .where({ bannerOrder: db.command.neq(null) }) // 只查被选中的
      .orderBy('bannerOrder', 'asc') // 按顺序
      .get()
      .then(res => {
        const fileList = res.data.map(item => item.fileID);
        if (fileList.length === 0) {
          console.log('管理员未选择任何图片，不显示图片');
          this.setData({
            bannerImages: []
          });
          return;
        }
        wx.cloud.getTempFileURL({
          fileList,
          success: urlRes => {
            // 过滤出成功获取到临时链接的图片
            const validUrls = urlRes.fileList
              .filter(file => file.tempFileURL) // 只保留成功获取到链接的图片
              .map(file => file.tempFileURL);

            console.log(`成功获取到${validUrls.length}张图片的临时链接`);
            this.setData({
              bannerImages: validUrls
            });
          },
          fail: (error) => {
            console.log('getTempFileURL调用失败:', error, '不显示图片');
            this.setData({
              bannerImages: []
            });
          }
        });
      })
      .catch(error => {
        console.log('查询数据库失败:', error, '不显示图片');
        this.setData({
          bannerImages: []
        });
      });
  },



  // 测试图片加载情况
  testImageLoad: function(imageUrls) {
    imageUrls.forEach((url, index) => {
      wx.getImageInfo({
        src: url,
        success: (res) => {
          console.log(`图片${index + 1}加载成功:`, url, res);
        },
        fail: (error) => {
          console.log(`图片${index + 1}加载失败:`, url, error);
        }
      });
    });
  },


  // 点击静态图片，弹出popup轮播图
  onStaticImageTap: function(e) {
    const currentIndex = e.currentTarget.dataset.index;
    const db = wx.cloud.database();
    db.collection('album_images')
      .where({ bannerOrder: db.command.neq(null) })
      .orderBy('bannerOrder', 'asc')
      .get()
      .then(res => {
        const fileList = res.data.map(item => item.fileID);
        if (fileList.length === 0) {
          this.setData({
            albumImages: [],
            showAlbumPopup: true,
            albumPopupIndex: 0
          });
          return;
        }
        wx.cloud.getTempFileURL({
          fileList,
          success: urlRes => {
            const urls = urlRes.fileList.map(file => file.tempFileURL);
            this.setData({
              albumImages: urls,
              showAlbumPopup: true,
              albumPopupIndex: currentIndex
            });
          },
          fail: () => {
            this.setData({
              albumImages: [],
              showAlbumPopup: true,
              albumPopupIndex: 0
            });
          }
        });
      });
  },
  // 关闭popup
  /**
   * closeAlbumPopup: 关闭相册弹窗的方法
   *
   * 功能说明：
   * 用户点击关闭按钮或弹窗外部区域时，隐藏相册图片弹窗
   *
   * 实现方式：
   * 将showAlbumPopup设为false，触发弹窗隐藏动画
   */
  closeAlbumPopup: function() {
    // 更新弹窗显示状态：false表示隐藏弹窗
    this.setData({ showAlbumPopup: false });
  },

  /**
   * previewAlbumImage: 预览相册图片的方法
   *
   * 功能说明：
   * 用户点击相册图片时，使用微信原生的图片预览功能
   * 支持图片缩放、滑动切换等原生体验
   *
   * @param {Object} e - 事件对象，包含点击的图片索引
   */
  previewAlbumImage: function(e) {
    // 从事件对象中获取图片索引：通过dataset获取自定义数据属性
    const index = e.currentTarget.dataset.index;

    /**
     * 调用微信原生图片预览API
     *
     * wx.previewImage：微信小程序提供的图片预览接口
     * 功能特点：
     * 1. 支持图片缩放、拖拽
     * 2. 支持左右滑动切换图片
     * 3. 支持长按保存图片
     * 4. 原生体验，性能优秀
     */
    wx.previewImage({
      current: this.data.albumImages[index], // 当前显示的图片URL
      urls: this.data.albumImages           // 所有图片URL数组，支持滑动切换
    });
  },

  /**
   * 更新tabBar的Badge状态
   * 检查未读消息和新课程，更新相应的Badge
   */
  async updateTabBarBadges() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.openid) {
        return;
      }

      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        // 检查未读消息
        try {
          const unreadResult = await wx.cloud.callFunction({
            name: 'notificationManagement',
            data: {
              action: 'getUnreadCount',
              data: {
                userId: userInfo.openid
              }
            }
          });

          if (unreadResult.result.success) {
            const unreadCount = unreadResult.result.data.unreadCount || 0;
            this.getTabBar().setProfileBadge(unreadCount > 0);
          }
        } catch (error) {
          console.error('检查未读消息失败:', error);
        }

        // 检查新课程
        try {
          const coursesResult = await wx.cloud.callFunction({
            name: 'notificationManagement',
            data: {
              action: 'checkNewCourses',
              data: {
                userId: userInfo.openid
              }
            }
          });

          if (coursesResult.result.success) {
            const hasNewCourses = coursesResult.result.data.hasNewCourses || false;
            this.getTabBar().setScheduleBadge(hasNewCourses);
          }
        } catch (error) {
          console.error('检查新课程失败:', error);
        }
      }
    } catch (error) {
      console.error('更新Badge状态失败:', error);
    }
  },

  
});

/**
 * 文件总结：index.js
 *
 * 这个文件实现了小程序的首页功能，是用户进入应用后看到的第一个页面。
 *
 * 主要特点：
 *
 * 1. 多媒体展示：
 *    - 背景图片预加载：提升用户体验，避免加载延迟
 *    - 相册管理集成：展示管理员上传的精美图片
 *    - 图片预览功能：支持原生的图片浏览体验
 *    - 视差滚动效果：增强页面的视觉层次感
 *
 * 2. 信息展示系统：
 *    - 动态联系信息：从数据库获取最新的门店信息
 *    - 弹窗展示：电话、地址、公告的详细信息展示
 *    - 优雅降级：信息加载失败时的友好处理
 *
 * 3. 用户交互优化：
 *    - 下拉刷新：支持用户手动刷新页面内容
 *    - 滚动效果：视差滚动增强视觉体验
 *    - 弹窗管理：多种信息弹窗的统一管理
 *    - 图片预览：集成微信原生图片预览功能
 *
 * 4. 技术架构：
 *    - 模块化设计：功能拆分为独立的方法
 *    - 异步处理：大量使用async/await处理异步操作
 *    - 错误处理：完善的try-catch错误处理机制
 *    - 数据绑定：响应式的数据更新和页面渲染
 *
 * 业务价值：
 *
 * 1. 品牌展示：
 *    - 精美的首页设计展示健身房的专业形象
 *    - 相册功能展示健身房的环境和设施
 *    - 视觉效果增强用户的第一印象
 *
 * 2. 信息传达：
 *    - 联系方式的便捷展示和使用
 *    - 重要公告的及时传达
 *    - 门店地址的准确展示
 *
 * 3. 用户引导：
 *    - 作为应用的入口页面，引导用户了解功能
 *    - 通过视觉设计激发用户的使用兴趣
 *    - 为后续的功能使用做好铺垫
 *
 * 与您熟悉的技术对比：
 *
 * - 页面结构：类似于ASP.NET的Page类或WinForms的Form类
 * - 数据绑定：类似于WPF的数据绑定机制
 * - 事件处理：类似于传统桌面应用的事件驱动模式
 * - 异步处理：类似于C#的async/await模式
 * - 生命周期：类似于ASP.NET页面的生命周期事件
 *
 * 小程序开发特点总结：
 *
 * 1. 文件结构：
 *    - JS文件：负责数据处理、事件响应、与后端交互（类似于Web的JS脚本）
 *    - WXML文件：负责页面结构和数据绑定（类似于HTML）
 *    - WXSS文件：负责页面样式（类似于CSS）
 *    - JSON文件：页面配置（类似于Web的meta信息和依赖声明）
 *
 * 2. 开发模式：
 *    - 事件驱动：用户操作触发事件，事件处理函数响应
 *    - 数据驱动：数据变化自动更新UI显示
 *    - 组件化：页面由多个组件组合而成
 *    - 生命周期：页面有完整的生命周期管理
 *
 * 3. 技术特色：
 *    - 响应式数据：setData机制实现数据与UI的同步
 *    - 原生能力：可以调用微信提供的原生API
 *    - 云开发：集成云数据库、云存储、云函数
 *    - 跨平台：一套代码在iOS和Android上运行
 *
 * 学习价值：
 *
 * 这个文件展示了现代前端开发的核心概念：
 * 1. 组件化和模块化的代码组织方式
 * 2. 异步编程和错误处理的最佳实践
 * 3. 用户体验优化的具体实现方法
 * 4. 数据驱动的UI更新机制
 * 5. 移动端开发的特殊考虑
 *
 * 对于有传统后端开发经验的开发者来说，小程序开发的事件、数据流、
 * 生命周期与主流前端框架（Vue/React）高度相似，便于快速上手。
 */