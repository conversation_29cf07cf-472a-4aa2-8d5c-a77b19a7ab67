/**
 * course-management.wxss - 课程管理页面样式文件
 *
 * 整理说明：
 * - 移除重复的选择器和属性声明
 * - 按功能模块组织样式规则
 * - 统一命名规范和代码格式
 * - 优化性能和可维护性
 */

/* ==================== 基础布局样式 ==================== */

/* 页面根元素和容器 */
page, .page {
  height: 100%;
}

/* 页面主容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden; /* 保持原有设置，通过z-index解决下拉菜单问题 */
  overflow-x: hidden;
}

/* ==================== 顶部区域样式 ==================== */

/* 顶部区域容器 */
.top-section {
  flex-shrink: 0;
  width: 100%;
  margin-bottom: 8px;
  background: transparent;
  border: none;
  position: relative;
  z-index: 100;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 选项卡区域容器 */
.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02), 0 0 0 1px rgba(0, 0, 0, 0.01);
  padding: 0 16px 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid #e7e7e7;
  z-index: 10;
}

.top-tabs-section:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 82, 217, 0.08);
}

/* 自定义顶部选项卡样式 */
.custom-top-tabs {
  background-color: transparent;
  border: none;
  border-radius: 0;
  overflow: visible;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 32rpx; /* 大字体 - 主要标题 */
  margin: 0;
  width: 100%;
  height: auto;
  min-height: 1px;
}

.custom-top-tabs .t-tabs__nav {
  padding: 0;
  height: auto;
  min-height: 1px;
  border-bottom: none;
  display: flex;
  align-items: center;
  background: transparent;
}

/* 选项卡项目样式 */
.custom-top-tabs .t-tabs__item {
  font-size: 32rpx !important; /* 大字体 - 主要标题 */
  font-weight: 500;
  padding: 14px 20px !important;
  height: auto;
  line-height: 1.4;
  min-height: 44px;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0;
  background: transparent !important;
  border-bottom: 3px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #666666 !important;
  position: relative;
}

.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 激活状态的选项卡 */
.custom-top-tabs .t-tabs__item--active {
  color: #0052d9 !important;
  font-weight: 600 !important;
  border-bottom-color: #0052d9 !important;
  background: transparent !important;
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

.custom-top-tabs .t-tabs__item--active::before {
  width: 60%;
}

/* 非激活状态的悬停效果 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  color: #333333 !important;
  background: transparent !important;
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%;
}

/* 隐藏默认指示器 */
.custom-top-tabs .t-tabs__track {
  display: none;
}

/* 操作按钮区域 */
.top-actions {
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 顶部操作按钮样式 */
.top-actions button.t-button {
  background: #52c41a !important;
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  margin-left: 12px !important;
  transition: background 0.2s !important;
  padding: 0 28px !important;
  min-width: unset !important;
  max-width: unset !important;
  width: auto !important;
}

.top-actions button.t-button:active,
.top-actions button.t-button:hover {
  background: #389e0d !important;
  color: #fff !important;
}

/* ==================== 筛选区域样式 ==================== */

/* 筛选区域容器 - 简化版本，移除视觉装饰 */
.filter-section {
  width: 100%;
  /* 移除背景、边框、内边距和阴影，让内容更简洁 */
  background: transparent;
  border: none;
  padding: 0;
  box-sizing: border-box;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  gap: 8px; /* 保留间距以维持布局 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: all 0.2s ease;
  overflow: visible; /* 保持visible，允许下拉菜单显示 */
  touch-action: none;
  -webkit-overflow-scrolling: auto;
  overscroll-behavior: contain;
  z-index: 90;
  position: relative;
}

/* 筛选区域内元素的触摸行为控制 */
.filter-section view,
.filter-section text,
.filter-section .search-actions-section,
.filter-section .collapsed-layout,
.filter-section .expanded-layout,
.filter-section .actions-container {
  touch-action: none;
  -webkit-overflow-scrolling: auto;
}

.filter-section .t-button,
.filter-section input,
.filter-section .search-input,
.filter-section .search-icon-only,
.filter-section .clear-icon,
.filter-section .collapse-icon {
  touch-action: manipulation;
}

/* 自定义子选项卡样式 */
.custom-sub-tabs {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  margin: 0;
  max-width: 100%;
}

.custom-sub-tabs .t-tabs__nav {
  padding: 0 8px;
}

/* ==================== 课程列表样式 ==================== */

/* 课程列表容器 */
.course-list {
  /* [Gemini] 新增了 flex: 1 和 min-height: 0 */
  /* flex: 1; 让课程列表这个容器，在它的父容器（.course-content）中撑满所有剩余的垂直空间。*/
  flex: 1;
  /* min-height: 0; 是一个关键的Flexbox技巧。当一个flex子元素内部有内容可能溢出时（比如我们的滚动列表），*/
  /* 这个属性确保该子元素可以正确地缩小和计算其内部滚动区域的高度，防止布局崩溃。*/
  min-height: 0;
  /* [Gemini] 新增了 display: flex, 让 .course-list 自身也成为一个flex容器 */
  /* 这样，它内部的 <scroll-view> 就能通过 height: 100% 正确地继承和计算高度。*/
  display: flex;
  flex-direction: column;
  margin-bottom: 0px;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/* 时间轴日期分隔 */
.timeline-date {
  margin: 8px;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #0052d9;
  font-weight: bold;
  text-align: left;
  position: relative;
  padding-left: 16px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 时间轴圆点装饰 */
.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

/* ==================== 课程卡片样式 ==================== */

/* 课程卡片基础样式 */
.course-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* 课程卡片激活状态 */
.course-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

/* 新加载卡片的滑入动画 */
.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 课程卡片头部样式 */
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

/* 课程标题样式 */
.course-title {
  font-size: 32rpx; /* 大字体 - 主要标题 */
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 课程状态标签样式 ==================== */

/* 状态标签基础样式 */
.course-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 状态标签颜色样式 */
.course-status.available { background-color: #e8f5e8; color: #52c41a; }
.course-status.booked { background-color: #e6f3ff; color: #0052d9; }
.course-status.full { background-color: #fff2e8; color: #fa8c16; }
.course-status.ended { background-color: #f0f0f0; color: #888; }
.course-status.online { background-color: #e8f5e8; color: #52c41a; }
.course-status.offline { background-color: #fff2e8; color: #fa8c16; }
.course-status.no-status { background-color: #f0f0f0; color: #999; }
.course-status.template-status { background-color: #e6f3ff; color: #1890ff; }

/* ==================== 课程信息项目样式 ==================== */

/* 课程信息列表容器 */
.course-info-list {
  margin-bottom: 10px;
}

/* 信息项目样式 */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  color: #0052d9;
  flex-shrink: 0;
}

/* 信息项文字样式 */
.info-item text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 课程卡片底部样式 ==================== */

/* 课程底部区域 */
.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

/* 操作按钮容器 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 2px;
  min-width: 0;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 60px;
  max-width: 80px;
}

/* 禁用按钮样式 */
.edit-disabled-btn {
  background-color: #f0f0f0 !important;
  border-color: #e0e0e0 !important;
  color: #bbb !important;
  cursor: not-allowed;
}

/* ==================== 模板列表样式 ==================== */

/* 模板列表容器 */
.template-list {
  /* [Gemini] 同 .course-list, 新增了 flex: 1 和 min-height: 0 */
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

.template-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px; /* 压缩间距 */
  gap: 6px; /* 压缩间距 */
}

.template-title {
  font-size: 32rpx; /* 大字体 - 主要标题 */
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-info-list {
  margin-bottom: 16px;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

/* 底部TabBar样式 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e7e7e7;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar {
  background-color: #ffffff;
}

.custom-tab-bar .t-tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar-item__text {
  font-size: 20rpx;
  margin-top: 4rpx;
  color: #666666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar-item--active .t-tab-bar-item__text {
  color: #0052d9;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 0 16px; /* 与collapse-header保持一致的左右缩进 */
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  justify-content: space-between; /* 确保移除按钮靠右对齐 */
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
  margin-right: 8px; /* 为移除图标留出空间 */
}

/* 移除学员图标样式 */
.remove-student-icon {
  flex-shrink: 0; /* 防止图标被压缩 */
  margin-left: auto; /* 确保图标靠右对齐 */
  padding: 4px; /* 增加点击区域 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  border-radius: 4px; /* 圆角效果 */
  transition: background-color 0.2s; /* 平滑的背景色过渡 */
}

/* 移除图标悬停效果 */
.remove-student-icon:hover {
  background-color: rgba(227, 77, 89, 0.1); /* 浅红色背景 */
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 20px 0;
}

/* 固定卡片宽度，防止内容拉伸 */
.course-card,
.template-card {
  width: 90vw;
  max-width: 700rpx;
  min-width: 320rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.course-content,
.template-content {
  /*
   * 可滚动内容区域样式
   * 占用剩余空间，允许内容滚动
   */
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 禁止水平滚动 */

  width: 100%;
  max-width: 700rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;

  /*
   * 滚动条样式优化（WebKit内核）
   * 在支持的浏览器中提供更美观的滚动条
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 筛选下拉触发器样式 - 参考album-management实现 */
.filter-dropdown-trigger {
  /*
   * 固定宽度设计：
   * - 增加宽度确保"历史活动"等文本能完整显示
   * - 不随搜索框展开/收起而变化
   * - 与其他UI元素保持一致的尺寸
   */
  width: 120px; /* 增加宽度，确保文字完整显示 */
  flex-shrink: 0; /* 防止被压缩 */

  /*
 * 字体和颜色：
 * - 使用系统字体栈，确保跨平台一致性
 * - 字体大小设置为28rpx，符合设计要求
 */
font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
font-size: 28rpx; /* 中字体 - 正文内容 */
}

/*
 * 筛选按钮样式
 * 模拟下拉选择器的外观
 */
.filter-button {
  /*
   * 尺寸和间距：
   * - 高度与搜索图标、操作按钮保持一致
   * - 内边距确保文本和图标有足够的呼吸空间
   */
  height: 32px;
  padding: 0 8px 0 12px;

  /*
   * 布局：
   * - 使用flex布局水平排列文本和箭头图标
   * - 垂直居中对齐
   * - 文本和图标之间有适当间距
   */
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;

  /*
   * 背景和边框：
   * - 白色背景，与其他输入组件保持一致
   * - 圆角与TDesign设计规范保持统一
   * - 边框颜色与其他组件协调
   */
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 4px;

  /*
   * 交互效果：
   * - 平滑的过渡动画
   * - 鼠标指针样式表明可点击
   */
  transition: all 0.2s ease;
  cursor: pointer;

  /*
   * 盒模型：
   * - 确保边框不会影响整体尺寸
   */
  box-sizing: border-box;
}

/*
 * 筛选按钮的悬停效果
 * 提供清晰的交互反馈
 */
.filter-button:hover {
  background-color: #f8f9fa;
  border-color: #0052d9;
}

/*
 * 筛选按钮的激活效果
 * 点击时的视觉反馈
 */
.filter-button:active {
  background-color: #e6f4ff;
  transform: scale(0.98);
}

/*
 * 筛选文本样式
 */
.filter-text {
  /*
   * 文本样式：
   * - 字重和颜色与其他UI元素保持一致
   * - 确保可读性
   */
  font-weight: 500;
  color: #333333;
  font-size: 28rpx; /* 中字体 - 正文内容 */

  /*
   * 布局：
   * - 文本不换行，超出部分隐藏
   * - 占据剩余空间
   */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/*
 * 筛选箭头图标样式
 */
.filter-arrow {
  color: #999999;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

/* 筛选箭头旋转状态 */
.filter-arrow.rotated {
  transform: rotate(180deg);
}

/* ==================== 筛选菜单样式（参考album-management实现）==================== */

/* 筛选菜单遮罩层 */
.filter-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 200rpx; /* 距离顶部的距离 */
}

/* 筛选菜单容器 */
.filter-menu {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.2), 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-width: 240rpx;
  max-width: 320rpx;
  animation: filterMenuIn 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 筛选菜单动画 */
@keyframes filterMenuIn {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 筛选菜单项 */
.filter-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333333;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1rpx solid #f5f5f5;
}

.filter-menu-item:last-child {
  border-bottom: none;
}

/* 筛选菜单项悬停效果 */
.filter-menu-item:hover {
  background-color: #f8f9fa;
}

/* 筛选菜单项激活状态 */
.filter-menu-item.active {
  background-color: #e6f4ff;
  color: #0052d9;
  font-weight: 500;
}

/* 筛选菜单项文本 */
.filter-menu-item-text {
  flex: 1;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

/* 筛选菜单项选中图标 */
.filter-menu-item-check {
  color: #0052d9;
  margin-left: 16rpx;
}



/* 骨架屏样式 */
.skeleton-card {
  background-color: #f3f3f3;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  width: 100%;
  min-height: 120px;
  animation: skeleton-fade 1.2s infinite linear;
}
.skeleton {
  background: linear-gradient(90deg, #f3f3f3 25%, #ececec 37%, #f3f3f3 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.2s infinite linear;
  border-radius: 6px;
}
.skeleton-text {
  height: 18px;
  width: 80%;
  margin-bottom: 8px; /* 压缩间距 */
}
.skeleton-block {
  height: 20px;
  width: 60px;
  margin-left: 8px;
}
.skeleton-btn {
  height: 28px;
  width: 60px;
  margin-right: 12px;
  display: inline-block;
}
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
@keyframes skeleton-fade {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 搜索和操作区域样式 - 简化版本 */
.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px; /* 元素间距 */
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 32px; /* 进一步减小高度 */

  /* 简化背景和边框 - 更轻量的视觉设计 */
  background: #ffffff;
  border-radius: 8px;
  padding: 8px; /* 适中的内边距 */
  border: 1px solid #e7e7e7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04); /* 轻微阴影保持层次感 */
}

/* 收起状态布局 - 筛选下拉 + 搜索图标 + 操作按钮 */
.search-actions-section.collapsed {
  justify-content: flex-start;
}

.collapsed-layout {
  display: flex;
  align-items: center;
  gap: 8px;
  /*
   * 使用flex: 1让collapsed-layout占据筛选下拉选择器之外的剩余空间
   * 这样可以确保操作按钮能够正确排列在右侧
   */
  flex: 1;

  /*
   * 确保collapsed-layout容器能够正确处理子元素溢出
   * min-width: 0 - 允许flex子元素缩小到内容宽度以下
   * overflow: hidden - 防止子元素溢出到容器外
   */
  min-width: 0;
  overflow: hidden;
}

/* 展开状态布局 - 筛选下拉 + 搜索框 */
.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  /*
   * 使用flex: 1让expanded-layout占据筛选下拉选择器之外的剩余空间
   * 这样搜索框可以占据最大可用宽度
   */
  flex: 1;
  height: 100%;
  display: flex;
  min-width: 0; /* 允许收缩 */
}

/* 搜索图标状态 - TDesign风格优化 */
.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px; /* 减小尺寸 */
  height: 32px; /* 减小尺寸 */
  background: #ffffff;
  border-radius: 4px; /* 减小圆角 */
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-icon-only:active {
  background: #f0f8ff;
  border-color: #0052d9;
  transform: scale(0.95); /* 添加按压效果 */
}

.search-toggle-icon {
  color: #666666;
  font-size: 28rpx; /* 中字体 - 图标大小 */
}

/* 展开的搜索输入框 - TDesign风格优化 */
.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 4px; /* 减小圆角 */
  padding: 6px 10px; /* 减小内边距 */
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 32px; /* 固定高度，与搜索图标一致 */
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow: 0 0 0 2px rgba(0, 82, 217, 0.1); /* 添加聚焦阴影 */
}

.search-icon {
  color: #999999;
  margin-right: 6px; /* 减小间距 */
  flex-shrink: 0;

  font-size: 28rpx; /* 中字体 - 图标大小 */
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 20px; /* 固定输入框高度 */
  line-height: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.search-input::placeholder {
  color: #999999;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.clear-icon,
.collapse-icon {
  color: #999999;
  margin-left: 6px; /* 减小间距 */
  flex-shrink: 0;
  cursor: pointer;
  font-size: 28rpx; /* 中字体 - 图标大小 */
  padding: 4px; /* 增加点击区域 */
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-icon:active,
.collapse-icon:active {
  color: #0052d9;
  background: rgba(0, 82, 217, 0.1);
  transform: scale(0.95); /* 添加按压效果 */
}

/* 搜索展开动画 */
@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 操作按钮容器 - TDesign风格优化 */
.actions-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  margin-left: auto; /* 靠右对齐 */

  /*
   * 防止按钮超出屏幕边界的关键设置
   * max-width: 限制容器最大宽度，避免按钮超出屏幕
   * overflow-x: auto - 当按钮过多时允许水平滚动
   * 计算方式：100vw - 搜索图标宽度(32px) - 间距(16px) = 约70%
   */
  max-width: 70vw;
  overflow-x: auto;

  /*
   * 滚动条样式优化
   * 在小程序中，滚动条通常是系统默认样式
   * 这里设置一些基础的滚动行为
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  scrollbar-width: none; /* Firefox隐藏滚动条 */
  -ms-overflow-style: none; /* IE隐藏滚动条 */
}

/* 隐藏webkit浏览器的滚动条 */
.actions-container::-webkit-scrollbar {
  display: none;
}

.actions-container .t-button {
  height: 32px; /* 固定高度 */
  padding: 0 12px; /* 减小内边距 */
  font-size: 28rpx; /* 中字体 - 正文内容 */

  /*
   * 防止按钮被压缩的关键设置
   * flex-shrink: 0 - 禁止按钮缩小，保持按钮的最小可读宽度
   * white-space: nowrap - 防止按钮文字换行
   * min-width: 设置按钮最小宽度，确保文字完整显示
   */
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 60px; /* 最小宽度，确保短文字按钮不会太小 */
  max-width: 80px; /* 最大宽度，防止长文字按钮过大 */
}

/**
 * course-management.wxss - 课程管理页面样式文件
 *
 * 整理说明：
 * - [已优化] 全面采用 rpx 单位，确保响应式布局的统一性。
 * - [已优化] 统一字体、间距、圆角等核心设计元素，参考 profile 页面规范。
 * - 按功能模块组织样式规则，并为主要样式添加详细注释。
 * - 优化性能和可维护性。
 */

/* ==================== 基础布局样式 ==================== */

/* 页面根元素和容器 */
page, .page {
  /*
   * height: 100%;
   * - `height` 属性设置元素的高度。
   * - `100%` 是一个相对单位，表示元素的高度将等于其父元素高度的100%。
   * - 这里让 `page` (页面根节点) 和 `.page` (通常是根节点的直接子元素) 高度撑满整个屏幕。
   */
  height: 100%;
}

/* 页面主容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  /*
   * padding: 32rpx;
   * - `padding` 是内边距，指元素边框与内容之间的空间。
   * - `32rpx` 是响应式像素单位，能根据屏幕宽度自适应，是小程序开发首选。
   *   常见取值有 16rpx, 24rpx, 32rpx, 40rpx 等，形成节奏感。
   */
  padding: 32rpx;
  /*
   * padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
   * - `padding-bottom` 单独设置底部的内边距。
   * - `calc()` 是CSS函数，用于进行计算。
   * - `env(safe-area-inset-bottom)` 是一个很重要的CSS变量，用于获取设备安全区域的底部边距，
   *   主要是为了适配 iPhone X 等有底部“小黑条”的全面屏手机，防止内容被遮挡。
   */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  /*
   * box-sizing: border-box;
   * - 这是非常重要的CSS属性，它改变了盒子模型的计算方式。
   * - 默认的 `content-box` 中，你设置的 `width` 和 `height` 只包含内容区，不包括 `padding` 和 `border`。
   * - `border-box` 中，`width` 和 `height` 则包含了内容、`padding` 和 `border`。这让布局计算更直观，
   *   比如你设置 `width: 100%`，它就是真的100%宽，不会因为加了 `padding` 而超出父容器。
   *   这和建筑师设计房间时，直接考虑房间的轴线尺寸或内空尺寸类似，而不是只算使用面积。
   */
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  overflow-x: hidden;
}

/* ==================== 顶部区域样式 ==================== */

.top-section {
  flex-shrink: 0;
  width: 100%;
  /*
   * margin-bottom: 16rpx;
   * - `margin` 是外边距，元素边框外的空间，用于控制元素间的距离。
   * - `16rpx` 遵循了 `8rpx` 的基本设计步进，是常用的间距值。
   */
  margin-bottom: 16rpx;
  position: relative;
  z-index: 100;
}

.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  /*
   * border: 1rpx solid #f0f0f0;
   * - `border` 设置元素的边框。
   * - `1rpx` 在这里是合适的，虽然 `1px` 也能画出最细的线，但在高清屏上 `1rpx` 也能达到类似效果且符合统一单位的原则。
   */
  border: 1rpx solid #f0f0f0;
  /*
   * border-radius: 16rpx;
   * - `border-radius` 用于设置元素的圆角。
   * - `16rpx` 是一个中等大小的圆角，参考了 `profile` 页面的设计。常见值有 8rpx, 12rpx, 16rpx, 24rpx。
   */
  border-radius: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02), 0 0 0 2rpx rgba(0, 0, 0, 0.01);
  padding: 0 32rpx 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1rpx solid #e7e7e7;
  z-index: 10;
}

.top-tabs-section:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04), 0 0 0 2rpx rgba(0, 82, 217, 0.08);
}

.custom-top-tabs {
  font-size: 32rpx; /* 大字体 - 主要标题 */
}

.custom-top-tabs .t-tabs__item {
  font-size: 32rpx !important; /* 大字体 - 主要标题 */
  font-weight: 500;
  /*
   * padding: 28rpx 40rpx !important;
   * - `padding` 的两个值分别代表上下的内边距和左右的内边距。
   * - `28rpx` 和 `40rpx` 提供了足够大的点击区域，提升用户体验。
   */
  padding: 28rpx 40rpx !important;
  line-height: 1.4;
  min-height: 88rpx;
  border-bottom: 6rpx solid transparent;
}

.custom-top-tabs .t-tabs__item::before {
  height: 4rpx;
}

.custom-top-tabs .t-tabs__item--active {
  border-bottom-color: #0052d9 !important;
}

/* ==================== 课程列表样式 ==================== */

.course-list {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.timeline-date {
  /*
   * margin: 16rpx;
   * - `16rpx` 的外边距，让日期标题和周围元素保持呼吸感。
   */
  margin: 16rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #0052d9;
  font-weight: bold;
  position: relative;
  padding-left: 32rpx;
}

.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  /*
   * width: 16rpx; height: 16rpx;
   * - 定义了时间轴上的装饰性圆点的大小。
   */
  width: 16rpx;
  height: 16rpx;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

/* ==================== 课程卡片样式 ==================== */

.course-card {
  background-color: #ffffff;
  /*
   * border-radius: 24rpx;
   * - 统一使用 `profile` 页面定义的 `24rpx` 大圆角，视觉上更柔和、现代。
   */
  border-radius: 24rpx;
  /*
   * padding: 24rpx;
   * - `24rpx` 的内边距，让卡片内容不显得拥挤。
   */
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  width: 100%;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.course-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-header {
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.course-title {
  font-size: 32rpx; /* 大字体 - 主要标题 */
  font-weight: 600;
  color: #333;
}

/* ==================== 课程状态标签样式 ==================== */

.course-status {
  /*
   * padding: 8rpx 16rpx;
   * - `8rpx` 上下内边距, `16rpx` 左右内边距。
   */
  padding: 8rpx 16rpx;
  /*
   * border-radius: 8rpx;
   * - 小圆角，适合标签这种小型组件。
   */
  border-radius: 8rpx;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  font-weight: 500;
}

/* ==================== 课程信息项目样式 ==================== */

.course-info-list {
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 8rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  gap: 16rpx;
}

/* ==================== 课程卡片底部样式 ==================== */

.course-footer {
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.action-buttons {
  gap: 12rpx;
  padding-bottom: 4rpx;
}

.action-buttons .t-button {
  min-width: 120rpx;
  max-width: 160rpx;
}

/* ==================== 模板列表样式 ==================== */

.template-list {
  flex: 1;
  min-height: 0;
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.template-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  width: 100%;
  box-sizing: border-box;
}

.template-header {
  margin-bottom: 16rpx;
  gap: 12rpx;
}

.template-title {
  font-size: 32rpx; /* 大字体 - 主要标题 */
  font-weight: 600;
  color: #333;
}

.template-info-list {
  margin-bottom: 32rpx;
}

.template-footer {
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

/* ==================== 已预约学员区域样式 ==================== */

.booked-students-section {
  /*
   * margin: 24rpx 0;
   * - 上下外边距 `24rpx`，左右为 `0`。
   */
  margin: 24rpx 0;
  border: 1rpx solid #f0f0f0;
  border-radius: 16rpx;
  overflow: hidden;
}

.collapse-header {
  padding: 24rpx 32rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.collapse-content {
  border-top: 1rpx solid #f0f0f0;
  padding: 0 32rpx;
}

.student-list {
  padding: 16rpx 0;
}

.student-item {
  padding: 12rpx 0;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.student-item t-icon {
  margin-right: 16rpx;
}

.student-name {
  margin-right: 16rpx;
}

.remove-student-icon {
  padding: 8rpx;
  border-radius: 8rpx;
}

.no-students {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 40rpx 0;
}

/* ==================== 筛选区域统一样式 ==================== */

.filter-dropdown-trigger {
  width: 240rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.filter-button {
  height: 64rpx;
  padding: 0 16rpx 0 24rpx;
  gap: 8rpx;
  border-radius: 8rpx;
}

.filter-text {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

/* ==================== 骨架屏及加载提示 ==================== */

.skeleton-card {
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  min-height: 240rpx;
}
.skeleton {
  border-radius: 12rpx;
}
.skeleton-text {
  height: 36rpx;
  margin-bottom: 16rpx;
}
.skeleton-block {
  height: 40rpx;
  width: 120rpx;
  margin-left: 16rpx;
}
.skeleton-btn {
  height: 56rpx;
  width: 120rpx;
  margin-right: 24rpx;
}

.loading-indicator {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 40rpx 0;
  gap: 16rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
}

.end-indicator {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 32rpx 0;
  letter-spacing: 2rpx;
}

/* ==================== 搜索和操作区域统一样式 ==================== */

.search-actions-section {
  gap: 16rpx;
  min-height: 64rpx;
  border-radius: 16rpx;
  padding: 16rpx;
}

.collapsed-layout {
  gap: 16rpx;
}

.search-icon-only {
  width: 64rpx;
  height: 64rpx;
  border-radius: 8rpx;
}

.search-toggle-icon {
  font-size: 28rpx; /* 中字体 - 图标大小 */
}

.search-input-container {
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  height: 64rpx;
}

.search-icon {
  margin-right: 12rpx;
  font-size: 28rpx; /* 中字体 - 图标大小 */
}

.search-input {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  height: 40rpx;
  line-height: 40rpx;
}

.search-input::placeholder {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.clear-icon,
.collapse-icon {
  margin-left: 12rpx;
  font-size: 28rpx; /* 中字体 - 图标大小 */
  padding: 8rpx;
  border-radius: 8rpx;
}

.actions-container {
  gap: 16rpx;
}

.actions-container .t-button {
  height: 64rpx;
  padding: 0 24rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  min-width: 120rpx;
  max-width: 160rpx;
}

/* ==================== 批量操作栏统一样式 ==================== */

.batch-actions-bar {
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  gap: 24rpx;

  /* 单行布局 */
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* 简洁的背景和边框 */
  background: #f8f9ff;
  border: 2rpx solid #e1f0ff;

  /* 轻微阴影 */
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.1);

  /* 出现动画 */
  animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-info {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333;
  font-weight: 500;
  flex-shrink: 0; /* 防止文字被压缩 */
}

.batch-buttons {
  gap: 16rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.batch-buttons .t-button {
  height: 56rpx; /* 稍微减小高度适应单行布局 */
  padding: 0 20rpx;
  font-size: 26rpx; /* 稍微减小字体 */
  min-width: 100rpx;
  max-width: 140rpx;
  border-radius: 12rpx;
  font-weight: 500;
  flex-shrink: 0; /* 防止按钮被压缩 */

  /* 按钮阴影效果 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.course-card.batch-mode {
  border: 4rpx solid #0052d9;
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.2);
  transform: translateY(-4rpx);
}

.course-title-row {
  gap: 16rpx;
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
}

/* ==================== 对话框和浮动按钮 ==================== */

.remove-student-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.remove-student-dialog-content text {
  margin-bottom: 16rpx;
}

.refund-checkbox-container {
  margin-top: 32rpx;
}

.refund-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.refund-label {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.countdown-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.countdown-dialog-content text {
  margin-bottom: 16rpx;
}

.back-to-today-fab {
  right: 32rpx;
  bottom: 120rpx;
}

/* ==================== 响应式布局调整 (基于rpx，大部分媒体查询可简化或移除) ==================== */

/* 
 * 由于我们已经全面采用 rpx 作为单位，小程序本身就会处理大部分的屏幕适配问题。
 * 因此，之前针对不同 px 宽度的 @media 查询可以被大大简化或移除。
 * 只有在极少数情况下，比如需要在超大屏或超小屏上做完全不同的布局时，才需要保留 @media 查询。
 * 这里的代码保留了部分查询作为示例，但在一个纯 rpx 的工作流中，它们很多都不是必需的。
 */

/* 小屏幕优化 (如 iPhone SE) */
@media (max-width: 375px) {
  .container {
    padding: 24rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  }

  .course-title,
  .template-title {
    font-size: 30rpx; /* 稍小于大字体 */
  }

  .info-item {
    font-size: 26rpx; /* 稍小于中字体 */
  }

  .actions-container .t-button,
  .batch-buttons .t-button {
    font-size: 24rpx; /* 小字体 */
    padding: 0 16rpx;
    min-width: 100rpx;
    max-width: 140rpx;
    height: 56rpx;
  }
}


/* 批量模式下的卡片样式 */
.course-card.batch-mode {
  border: 2px solid #0052d9;
  box-shadow: 0 4px 12px rgba(0, 82, 217, 0.2);
  transform: translateY(-2px);
}

.course-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  overflow: hidden;
}

.course-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.custom-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.check-icon {
  color: #ffffff;
}

/* 移除学员确认对话框样式 */
.remove-student-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  line-height: 1.6;
}

.remove-student-dialog-content text {
  display: block;
  margin-bottom: 8px;
}

.remove-student-dialog-content .student-name {
  font-weight: bold;
  color: #333;
}

.refund-checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

.refund-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.refund-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.refund-checkbox .check-icon {
  color: #ffffff;
}

.refund-label {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333;
}

/* 倒计时确认对话框样式 */
.countdown-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  line-height: 1.6;
}

.countdown-dialog-content text {
  display: block;
  margin-bottom: 8px;
}

.countdown-dialog-content .course-name {
  font-weight: bold;
  color: #d9534f;
}

/* 加载指示器样式 */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 到底部提示样式 */
.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 16px 0;
  letter-spacing: 1px;
}

/* 自定义下拉刷新样式 */
.custom-refresher {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  background-color: #f5f5f5;
}

.refresher-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresher-icon {
  font-size: 32rpx; /* 大字体 - 图标 */
  color: #666;
  transition: transform 0.2s ease;
}

.refresher-text {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  color: #666;
}

/* 回到顶部按钮样式 */
.back-to-today-fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  z-index: 100;
}

.back-to-today-fab .t-fab__btn {
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

/* 响应式布局调整 */
/* 针对小屏幕进行优化，确保标签栏和按钮在不同尺寸下都能正确显示 */

/* 中等小屏幕优化 (iPhone SE, 小屏Android) */
@media (max-width: 414px) {
  .actions-container {
    /*
     * 在中等小屏幕上进一步限制按钮容器宽度
     * 为搜索图标和间距留出更多空间
     */
    max-width: 65vw;
    gap: 6px; /* 减小按钮间距 */
  }

  .actions-container .t-button {
    /*
     * 优化按钮尺寸以适应中等小屏幕
     * 减小内边距和字体大小，但保持可读性
     */
    padding: 0 10px;
    font-size: 26rpx; /* 稍小于中字体 */
    min-width: 55px; /* 稍微减小最小宽度 */
    max-width: 75px;
  }

  /* 批量操作栏在中等小屏幕的优化 - 单行布局 */
  .batch-actions-bar {
    /*
     * 在中等小屏幕上减小内边距，为内容腾出更多空间
     * 保持单行布局的间距
     */
    padding: 16rpx 20rpx; /* 使用rpx单位 */
    gap: 20rpx; /* 使用rpx单位 */
  }

  .batch-info {
    /*
     * 优化批量信息文字显示
     * 减小字体但保持可读性
     */
    font-size: 26rpx; /* 稍小于中字体 */
  }

  .batch-buttons {
    gap: 12rpx; /* 使用rpx单位 */
  }

  .batch-buttons .t-button {
    /*
     * 批量操作按钮在中等小屏幕的优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 56rpx; /* 使用rpx单位 */
    padding: 0 16rpx;
    font-size: 26rpx; /* 稍小于中字体 */
    min-width: 100rpx;
    max-width: 140rpx;
  }
}

/* 小屏幕优化 (iPhone SE第一代等) */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 14px !important; /* 减小水平内边距 */
    font-size: 30rpx !important; /* 稍小于大字体 */
    min-height: 42px; /* 减小最小高度 */
  }

  .actions-container {
    /*
     * 在小屏幕上进一步优化按钮容器
     * 确保即使是最长的按钮文字也能完整显示
     */
    max-width: 60vw;
    gap: 4px; /* 进一步减小间距 */
  }

  .actions-container .t-button {
    /*
     * 小屏幕下的按钮优化
     * 平衡可读性和空间利用率
     */
    padding: 0 8px;
    font-size: 24rpx; /* 小字体 */
    min-width: 50px;
    max-width: 70px;
  }

  /* 批量操作栏在小屏幕的优化 - 单行布局 */
  .batch-actions-bar {
    /*
     * 在小屏幕上进一步减小内边距
     * 最大化可用空间，保持单行布局
     */
    padding: 12rpx 16rpx; /* 使用rpx单位 */
    gap: 16rpx; /* 使用rpx单位 */
  }

  .batch-info {
    /*
     * 小屏幕下的批量信息优化
     * 进一步减小字体
     */
    font-size: 24rpx; /* 小字体 */
  }

  .batch-buttons {
    gap: 8rpx; /* 使用rpx单位 */
  }

  .batch-buttons .t-button {
    /*
     * 小屏幕下的批量操作按钮优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 52rpx; /* 使用rpx单位 */
    padding: 0 12rpx;
    font-size: 24rpx; /* 小字体 */
    min-width: 90rpx;
    max-width: 120rpx;
  }
}

/* 超小屏幕优化 (iPhone 5/SE第一代等) */
@media (max-width: 375px) {
  .container {
    padding: 8px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }

  .course-card,
  .template-card {
    padding: 12px;
  }

  .course-title,
  .template-title {
    font-size: 30rpx; /* 稍小于大字体 */
  }

  .info-item {
    font-size: 26rpx; /* 稍小于中字体 */
  }

  .booking-tab {
    font-size: 26rpx; /* 稍小于中字体 */
    padding: 6px 4px;
  }

  .actions-container {
    /*
     * 超小屏幕的极限优化
     * 确保在最小的屏幕上也能正常显示
     */
    max-width: 55vw;
    gap: 3px; /* 最小间距 */
  }

  .actions-container .t-button {
    /*
     * 超小屏幕下的按钮设置
     * 在保持可用性的前提下最大化空间利用
     */
    font-size: 22rpx; /* 小于小字体 */
    padding: 0 6px;
    min-width: 45px;
    max-width: 65px;
    height: 30px; /* 稍微减小高度 */
  }

  /* 批量操作栏在超小屏幕的极限优化 - 单行布局 */
  .batch-actions-bar {
    /*
     * 超小屏幕的极限优化
     * 最小化内边距，最大化可用空间，保持单行布局
     */
    padding: 8rpx 12rpx; /* 使用rpx单位 */
    gap: 12rpx; /* 使用rpx单位 */
  }

  .batch-info {
    /*
     * 超小屏幕下的批量信息极限优化
     * 保持信息可读的前提下最小化占用空间
     */
    font-size: 22rpx;
  }

  .batch-buttons {
    gap: 6rpx; /* 使用rpx单位 */
  }

  .batch-buttons .t-button {
    /*
     * 超小屏幕下的批量操作按钮极限优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 48rpx; /* 使用rpx单位 */
    padding: 0 10rpx;
    font-size: 22rpx;
    min-width: 80rpx;
    max-width: 110rpx;
  }

  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
  }
}