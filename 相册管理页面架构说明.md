# 相册管理页面 (album-management) 架构说明

## 核心问题：为什么此页面不需要云函数？

`album-management` 页面不依赖云函数来完成其核心功能，其主要原因是该页面采用了**小程序前端直连云开发资源**的模式。

所有的数据和文件操作，例如图片的上传、下载、删除以及文件夹的创建和管理，都是通过小程序端提供的云开发 SDK 直接与**云数据库**和**云存储**进行交互。

### 主要实现方式：

1.  **直连云数据库**：
    *   页面逻辑通过 `wx.cloud.database()` 初始化数据库实例。
    *   使用 `db.collection('album_images')` 和 `db.collection('album_folders')` 等方法，直接在前端对数据库集合执行增、删、改、查 (CRUD) 操作。

2.  **直连云存储**：
    *   **图片上传**：调用 `wx.cloud.uploadFile()` 将用户选择的图片直接上传到云存储中。
    *   **图片删除**：调用 `wx.cloud.deleteFile()` 删除云存储中对应的图片文件。
    *   **图片展示**：调用 `wx.cloud.getTempFileURL()` 获取文件的临时访问链接，用于在页面上渲染和预览。

### 何时需要云函数？

虽然此页面不直接依赖云函数，但在以下场景中，云函数是必需的：

*   **复杂数据处理**：当需要执行复杂的数据聚合、分析或批量处理时，为了不占用前端资源并提高性能，应将逻辑放在云函数中。
*   **高权限操作**：对于需要管理员权限或涉及敏感信息的操作（例如，修改其他用户信息、处理支付回调等），必须在云函数中完成，以保证安全性。
*   **定时任务**：需要按计划自动执行的任务，例如代码中提到的 `autoCleanTrash` (自动清理回收站) 功能，其背后就需要一个定时触发的云函数来执行。

### 总结

`album-management` 页面的功能逻辑虽然完整，但其核心是围绕文件和数据库记录的直接操作。微信小程序云开发提供的强大前端 SDK 使得这种“前端直连”模式成为可能，从而简化了开发流程，减少了对后端云函数的依赖。
