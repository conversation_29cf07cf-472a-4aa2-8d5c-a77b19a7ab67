/**
 * 回收站管理工具
 * 提供软删除、恢复、永久删除和自动清理功能
 */

const db = wx.cloud.database();

/**
 * 确保回收站文件夹存在
 */
async function ensureTrashFolderExists() {
  try {
    // 检查回收站文件夹是否存在
    const trashFolder = await db.collection('album_folders').doc('folder_trash').get();

    if (!trashFolder.data) {
      // 回收站文件夹不存在，创建它
      await db.collection('album_folders').doc('folder_trash').set({
        data: {
          name: '回收站',
          type: 'system',
          systemType: 'trash',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        }
      });
      console.log('回收站文件夹自动创建成功');
    }
  } catch (error) {
    if (error.errCode === -502001) {
      // 文档不存在，创建回收站文件夹
      await db.collection('album_folders').doc('folder_trash').set({
        data: {
          name: '回收站',
          type: 'system',
          systemType: 'trash',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        }
      });
      console.log('回收站文件夹自动创建成功');
    } else {
      console.error('检查回收站文件夹时出错:', error);
    }
  }
}

/**
 * 软删除图片（移入回收站）
 */
export async function moveToTrash(imageIds) {
  try {
    // 确保回收站文件夹存在
    await ensureTrashFolderExists();

    // 确保imageIds是数组
    const ids = Array.isArray(imageIds) ? imageIds : [imageIds];
    
    // 批量更新图片状态，保存原文件夹信息
    const updatePromises = ids.map(async imageId => {
      // 先获取图片当前信息
      const imageResult = await db.collection('album_images').doc(imageId).get();
      if (!imageResult.data) return;

      const image = imageResult.data;

      // 保存删除前的文件夹信息
      const beforeDeleteInfo = {
        isFavorite: image.isFavorite || false,
        bannerOrder: image.bannerOrder || null,
        folderIds: image.folderIds || []
      };

      return db.collection('album_images').doc(imageId).update({
        data: {
          isDeleted: true,
          deletedTime: new Date(),
          updateTime: new Date(),
          beforeDeleteInfo: beforeDeleteInfo, // 保存删除前的状态
          // 清除首页展示状态
          bannerOrder: null,
          // 清除收藏状态
          isFavorite: false
        }
      });
    });
    
    await Promise.all(updatePromises);
    
    // 更新所有相关文件夹的图片数量
    await updateAllFolderCounts();
    
    return {
      success: true,
      message: `已将 ${ids.length} 张图片移入回收站`,
      deletedCount: ids.length
    };
  } catch (error) {
    console.error('移入回收站失败:', error);
    return {
      success: false,
      message: '移入回收站失败',
      error
    };
  }
}

/**
 * 从回收站恢复图片
 */
export async function restoreFromTrash(imageIds) {
  try {
    // 确保imageIds是数组
    const ids = Array.isArray(imageIds) ? imageIds : [imageIds];
    
    // 批量恢复图片
    const updatePromises = ids.map(async imageId => {
      // 先获取图片当前信息
      const imageResult = await db.collection('album_images').doc(imageId).get();
      if (!imageResult.data) return;

      const image = imageResult.data;
      const beforeDeleteInfo = image.beforeDeleteInfo || {};

      // 恢复删除前的文件夹状态
      const updateData = {
        isDeleted: false,
        deletedTime: db.command.remove(), // 移除删除时间字段
        beforeDeleteInfo: db.command.remove(), // 移除备份信息
        updateTime: new Date(),
        // 恢复原有的文件夹关联
        isFavorite: beforeDeleteInfo.isFavorite || false,
        bannerOrder: beforeDeleteInfo.bannerOrder || null,
        folderIds: beforeDeleteInfo.folderIds || []
      };

      return db.collection('album_images').doc(imageId).update({
        data: updateData
      });
    });
    
    await Promise.all(updatePromises);
    
    // 更新所有相关文件夹的图片数量
    await updateAllFolderCounts();
    
    return {
      success: true,
      message: `已恢复 ${ids.length} 张图片`,
      restoredCount: ids.length
    };
  } catch (error) {
    console.error('恢复图片失败:', error);
    return {
      success: false,
      message: '恢复图片失败',
      error
    };
  }
}

/**
 * 永久删除图片
 */
export async function permanentDelete(imageIds) {
  try {
    // 确保imageIds是数组
    const ids = Array.isArray(imageIds) ? imageIds : [imageIds];
    
    // 获取要删除的图片信息（用于删除云存储文件）
    const imagePromises = ids.map(imageId => {
      return db.collection('album_images').doc(imageId).get();
    });
    
    const imageResults = await Promise.all(imagePromises);
    const fileIds = imageResults
      .filter(result => result.data)
      .map(result => result.data.fileID);
    
    // 删除云存储文件
    if (fileIds.length > 0) {
      try {
        await wx.cloud.deleteFile({
          fileList: fileIds
        });
      } catch (error) {
        console.warn('删除云存储文件时出错:', error);
        // 继续执行数据库删除，即使云存储删除失败
      }
    }
    
    // 删除数据库记录
    const deletePromises = ids.map(imageId => {
      return db.collection('album_images').doc(imageId).remove();
    });
    
    await Promise.all(deletePromises);
    
    return {
      success: true,
      message: `已永久删除 ${ids.length} 张图片`,
      deletedCount: ids.length
    };
  } catch (error) {
    console.error('永久删除图片失败:', error);
    return {
      success: false,
      message: '永久删除失败',
      error
    };
  }
}

/**
 * 获取回收站中的图片
 */
export async function getTrashImages(page = 0, pageSize = 20) {
  try {
    const result = await db.collection('album_images')
      .where({ isDeleted: true })
      .orderBy('deletedTime', 'desc')
      .skip(page * pageSize)
      .limit(pageSize)
      .get();
    
    // 获取临时访问链接
    const fileList = result.data.map(item => item.fileID);
    if (fileList.length === 0) {
      return {
        success: true,
        data: [],
        hasMore: false
      };
    }
    
    const urlResult = await wx.cloud.getTempFileURL({
      fileList: fileList
    });
    
    const urlMap = {};
    urlResult.fileList.forEach(file => {
      urlMap[file.fileID] = file.tempFileURL;
    });
    
    const imagesWithUrls = result.data.map(item => ({
      ...item,
      tempFileURL: urlMap[item.fileID] || ''
    }));
    
    return {
      success: true,
      data: imagesWithUrls,
      hasMore: result.data.length === pageSize
    };
  } catch (error) {
    console.error('获取回收站图片失败:', error);
    return {
      success: false,
      message: '获取回收站图片失败',
      error,
      data: [],
      hasMore: false
    };
  }
}

/**
 * 清空回收站（永久删除所有回收站图片）
 */
export async function emptyTrash() {
  try {
    // 分批获取回收站中的所有图片
    let allTrashImages = [];
    let page = 0;
    let hasMore = true;
    
    while (hasMore) {
      const result = await db.collection('album_images')
        .where({ isDeleted: true })
        .skip(page * 100)
        .limit(100)
        .get();
      
      allTrashImages = allTrashImages.concat(result.data);
      hasMore = result.data.length === 100;
      page++;
    }
    
    if (allTrashImages.length === 0) {
      return {
        success: true,
        message: '回收站已为空',
        deletedCount: 0
      };
    }
    
    // 永久删除所有图片
    const imageIds = allTrashImages.map(image => image._id);
    const result = await permanentDelete(imageIds);
    
    return result;
  } catch (error) {
    console.error('清空回收站失败:', error);
    return {
      success: false,
      message: '清空回收站失败',
      error
    };
  }
}

/**
 * 自动清理超过30天的回收站图片
 */
export async function autoCleanTrash() {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // 查找超过30天的删除图片
    const result = await db.collection('album_images')
      .where({
        isDeleted: true,
        deletedTime: db.command.lt(thirtyDaysAgo)
      })
      .get();
    
    if (result.data.length === 0) {
      return {
        success: true,
        message: '没有需要清理的图片',
        cleanedCount: 0
      };
    }
    
    // 永久删除这些图片
    const imageIds = result.data.map(image => image._id);
    const deleteResult = await permanentDelete(imageIds);
    
    if (deleteResult.success) {
      console.log(`自动清理完成，删除了 ${deleteResult.deletedCount} 张过期图片`);
    }
    
    return {
      success: deleteResult.success,
      message: `自动清理完成，删除了 ${deleteResult.deletedCount} 张过期图片`,
      cleanedCount: deleteResult.deletedCount
    };
  } catch (error) {
    console.error('自动清理回收站失败:', error);
    return {
      success: false,
      message: '自动清理失败',
      error
    };
  }
}

/**
 * 获取回收站统计信息
 */
export async function getTrashStats() {
  try {
    // 统计回收站图片总数
    const totalResult = await db.collection('album_images')
      .where({ isDeleted: true })
      .count();
    
    // 统计即将过期的图片数量（25天以上）
    const twentyFiveDaysAgo = new Date();
    twentyFiveDaysAgo.setDate(twentyFiveDaysAgo.getDate() - 25);
    
    const expiringSoonResult = await db.collection('album_images')
      .where({
        isDeleted: true,
        deletedTime: db.command.lt(twentyFiveDaysAgo)
      })
      .count();
    
    return {
      success: true,
      totalCount: totalResult.total,
      expiringSoonCount: expiringSoonResult.total
    };
  } catch (error) {
    console.error('获取回收站统计失败:', error);
    return {
      success: false,
      totalCount: 0,
      expiringSoonCount: 0,
      error
    };
  }
}

/**
 * 更新所有文件夹的图片数量
 * 在图片删除/恢复后调用
 */
async function updateAllFolderCounts() {
  try {
    // 获取所有文件夹
    const foldersResult = await db.collection('album_folders').get();
    
    // 更新每个文件夹的图片数量
    const updatePromises = foldersResult.data.map(folder => {
      return updateFolderImageCount(folder._id, folder.type, folder.systemType);
    });
    
    await Promise.all(updatePromises);
  } catch (error) {
    console.error('更新文件夹图片数量失败:', error);
  }
}

/**
 * 更新单个文件夹的图片数量
 */
async function updateFolderImageCount(folderId, folderType, systemType) {
  try {
    let count = 0;
    
    if (folderType === 'system') {
      if (systemType === 'favorite') {
        // 收藏夹：统计收藏且未删除的图片
        const result = await db.collection('album_images')
          .where({
            isFavorite: true,
            isDeleted: false
          })
          .count();
        count = result.total;
      } else if (systemType === 'banner') {
        // 首页展示：统计有bannerOrder且未删除的图片
        const result = await db.collection('album_images')
          .where({
            bannerOrder: db.command.neq(null),
            isDeleted: false
          })
          .count();
        count = result.total;
      }
    } else {
      // 自定义文件夹：统计包含该文件夹ID且未删除的图片
      const result = await db.collection('album_images')
        .where({
          folderIds: db.command.in([folderId]),
          isDeleted: false
        })
        .count();
      count = result.total;
    }
    
    // 更新文件夹的图片数量
    await db.collection('album_folders').doc(folderId).update({
      data: {
        imageCount: count,
        updateTime: new Date()
      }
    });
    
    return count;
  } catch (error) {
    console.error(`更新文件夹 ${folderId} 图片数量失败:`, error);
    return 0;
  }
}
