# Profile 页面字体设计规范

为了确保 `profile` 页面在不同设备上都具有统一、清晰且富有层次感的视觉体验，我们制定了以下字体设计规范。所有开发人员在修改或新增该页面的样式时，应严格遵守此规范。

---

### 1. 统一字体族 (Font Family)

- **核心字体**: 页面默认且唯一的字体为 **苹方 (PingFang SC)**。
- **备用策略**: 为了兼容不同系统，设置了从 `PingFang SC` 到无衬线字体的优雅降级策略。

**实现代码:**
```css
.container {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
```

---

### 2. 标准化字体大小 (Font Size)

页面严格限制只使用以下三种标准化的字体大小，以确保视觉层级的清晰可辨。

#### a. 大字体 (`32rpx`)

- **用途**: 用于最重要的标题和信息，吸引用户的主要注意力。
- **应用场景**:
  - 用户昵称 (`.profile-name`)
  - 登录页欢迎语 (`.login-text`)
  - 统计数据中的数字 (`.stat-number-new`)
- **实现代码**:
  ```css
  .font-large { font-size: 32rpx; }
  ```

#### b. 中字体 (`28rpx`)

- **用途**: 用于正文内容、功能列表项和次级标题，是页面中最常见的内容字体。
- **应用场景**:
  - 功能项标签 (`.function-item text`)
  - 卡片标题 (`.card-title`)
  - 登录按钮文字 (`.wx-login-btn`)
- **实现代码**:
  ```css
  .font-medium { font-size: 28rpx; }
  ```

#### c. 小字体 (`24rpx`)

- **用途**: 用于辅助性、提示性信息和标签，提供额外信息但不过分干扰主内容。
- **应用场景**:
  - 登录页提示文字 (`.login-tip`)
  - 用户协议链接 (`.agreement-link`)
  - 统计数据中的标签 (`.stat-label-new`)
- **实现代码**:
  ```css
  .font-small { font-size: 24rpx; }
  ```

---

**总结:**

通过严格遵守这套字体规范，我们旨在构建一个视觉统一、主次分明、阅读舒适的用户界面。请在后续开发中继续遵循这些标准。
