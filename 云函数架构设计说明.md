# 云函数架构设计说明

## 核心问题：为什么项目需要使用云函数？

与 `album-management` 页面可以直接操作数据库不同，`cloudfunctions` 目录下的云函数主要用于处理那些**不能或不应该**在小程序前端执行的任务。这些任务的核心需求可以归纳为以下几点：

### 1. 权限控制与安全 (Security and Authorization)

这是使用云函数最关键的原因。前端代码对用户是透明的，任何在前端执行的逻辑都可能被绕过或篡改。因此，所有涉及权限判断和敏感操作的逻辑都必须放在云函数（即服务端）中执行，以确保安全。

- **典型案例**: `adminManagement` 云函数。
- **具体实现**: 在执行 `deleteCourse` (删除课程)、`updateUserRole` (更新用户角色) 等高危操作前，云函数会先调用 `verifyAdmin(openid)` 方法。该方法在服务端查询数据库，验证发起请求的用户是否拥有“管理员”角色。如果验证不通过，操作将被直接拒绝。这种安全机制是无法在前端可靠实现的。

### 2. 复杂业务逻辑与原子操作 (Complex Logic & Atomic Operations)

当一个功能需要连续操作多个数据集合（表），并且必须保证所有操作要么全部成功、要么全部失败时，就需要使用云函数来确保其“原子性”。

- **典型案例**: `bookingManagement` 云函数中的 `bookCourse` (预约课程) 功能。
- **具体实现**: 一次课程预约涉及到以下步骤：
    1.  查询课程是否存在且有余量。
    2.  查询用户是否有有效的会员卡。
    3.  扣减会员卡的使用次数。
    4.  创建一条新的预约记录。
    5.  调用 `notificationManagement` 云函数发送通知。

    这一系列操作必须捆绑执行。如果将它们分散在前端，一旦中间某个环节（如网络中断）出错，就可能导致数据不一致（例如，扣了卡次但没约上课）。云函数可以将这些操作封装在一起，并通过数据库事务等机制保证其原子性。

### 3. 定时与自动化任务 (Scheduled & Automated Tasks)

小程序本身不具备在后台定时执行任务的能力。所有需要按计划自动运行的功能，都必须依赖服务端的定时任务触发器。

- **典型案例**: `notificationScheduler` 云函数。
- **具体实现**: 该云函数的 `config.json` 文件中配置了标准的 Cron 表达式，定义了何时自动执行特定任务。
    - `"config": "0 0 20 * * *"`：表示在每天晚上8点，自动触发云函数，执行“发送明日课程提醒”的任务。
    - `"config": "0 0 2 * * *"`：表示在每天凌晨2点，自动清理已过期的通知数据。

### 4. 服务解耦与数据聚合 (Service Decoupling & Data Aggregation)

将不同功能模块化，并通过云函数相互调用，可以使系统架构更清晰、更易于维护。

- **服务解耦**: `bookingManagement` 在预约成功后，只需调用 `notificationManagement` 并告知其“发送预约成功通知”，而无需关心通知的具体实现方式。这使得通知模块可以独立迭代，不影响预约模块。
- **数据聚合**: 前端页面有时需要展示来自多个数据集合的整合信息。为了提升性能、减少前端的网络请求次数，可以在云函数中预先处理好这些数据。
    - **典型案例**: `adminManagement` 的 `getCourseListPaged` 函数使用数据库的 `aggregate` 和 `lookup` 操作，将课程数据和预约数据在服务端直接聚合，计算出每门课的已报名人数，然后一次性返回给前端。

---

### 结论

`album-management` 页面处理的是简单的、用户自身的、非敏感的数据操作，因此前端直连数据库是最高效的方式。而 `cloudfunctions` 目录下的云函数则承担了整个应用的安全、核心业务逻辑、自动化和性能优化的重任，是小程序能够稳定、安全运行的基石。